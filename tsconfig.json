{"compilerOptions": {"module": "commonjs", "declaration": true, "strict": true, "lib": ["esnext"], "removeComments": false, "noLib": false, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "target": "es6", "sourceMap": true, "outDir": "./dist", "rootDir": ".", "skipLibCheck": true, "allowJs": true}, "include": ["lib/**/*", "tests/**/*", "examples/**/*"], "exclude": ["node_modules", "dist"]}