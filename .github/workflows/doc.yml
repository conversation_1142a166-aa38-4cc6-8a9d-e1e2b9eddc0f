# This workflow deploy typedoc generated documentation to gh-pages branch

name: doc

on:
  push:
    branches: [main]

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@3df4ab11eba7bda6032a0b82a6bb43b11571feac # v4

      - name: Install pnpm
        uses: pnpm/action-setup@v2

      - name: Use Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: pnpm install

      - name: Lint
        run: pnpm run lint:dontfix

      - name: Check Formatting
        run: pnpm run format:dontfix

      - name: Coverage
        run: pnpm run test:cov

      - name: Generate documentation
        run: pnpm run doc

      - name: Deploy to Github Pages
        uses: crazy-max/ghaction-github-pages@v4.2.0
        with:
          # Build directory to deploy
          build_dir: docs
          # The author name and email address
          author: typedoc
          # Allow Jekyll to build your site
          jekyll: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
