{"name": "nest-typed-config", "version": "2.10.1", "description": "Intuitive, type-safe configuration module for Nest framework", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "packageManager": "pnpm@7.33.7", "url": "https://github.com/Nikaple/nest-typed-config", "homepage": "https://github.com/Nikaple/nest-typed-config", "files": ["index.js", "index.d.ts", "index.ts", "lib", "dist"], "repository": {"type": "git", "url": "https://github.com/Nikaple/nest-typed-config"}, "bugs": {"url": "https://github.com/Nikaple/nest-typed-config/issues"}, "scripts": {"prepare": "husky install", "build": "rimraf -rf dist && tsc -p tsconfig.build.json", "test": "jest --runInBand", "test:watch": "jest --runInBand --watch", "test:cov": "jest --runInBand --coverage", "doc": "typedoc lib/index.ts --tsconfig tsconfig.build.json", "lint": "eslint {lib/**/*.ts,tests/**/*.ts,examples/**/*.ts} --fix", "lint:dontfix": "eslint {lib/**/*.ts,tests/**/*.ts,examples/**/*.ts}", "format": "prettier --write .", "format:dontfix": "prettier --check .", "prepublish:npm": "npm run build", "prerelease": "npm run build", "release": "semantic-release"}, "dependencies": {"chalk": "4.1.2", "class-transformer": "0.5.1", "class-validator": "^0.14.0", "debug": "4.3.4", "lodash.frompairs": "4.0.1", "lodash.merge": "4.6.2", "set-value": "^4.1.0"}, "pnpm": {"overrides": {"rxjs": "7.8.0", "axios": "0.27.2"}}, "devDependencies": {"@commitlint/cli": "17.7.1", "@iarna/toml": "2.2.5", "@latipun7/commitlintrc": "1.1.3", "@latipun7/releaserc": "^2.1.0", "@nestjs/axios": "3.0.0", "@nestjs/cli": "10.1.17", "@nestjs/common": "10.2.5", "@nestjs/core": "10.2.5", "@nestjs/platform-express": "10.2.5", "@nestjs/testing": "10.2.5", "@types/debug": "4.1.8", "@types/express": "4.17.17", "@types/jest": "29.5.5", "@types/lodash.frompairs": "4.0.7", "@types/lodash.merge": "4.6.7", "@types/node": "18.17.17", "@types/set-value": "^4.0.1", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "axios": "1.5.0", "cosmiconfig": "8.3.6", "dotenv": "16.3.1", "dotenv-expand": "10.0.0", "eslint": "7.32.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-import": "2.28.1", "eslint-plugin-prettier": "4.2.1", "husky": "8.0.3", "jest": "29.7.0", "lint-staged": "14.0.1", "parse-json": "8.3.0", "prettier": "2.8.8", "reflect-metadata": "0.1.13", "rimraf": "5.0.1", "rxjs": "7.8.1", "semantic-release": "^22.0.0", "ts-jest": "29.1.1", "typedoc": "0.25.1", "typescript": "5.2.2", "yaml": "2.3.2"}, "optionalDependencies": {"@iarna/toml": ">= 2.2.5", "@nestjs/axios": ">= 0.1.0", "cosmiconfig": ">= 8.0.0", "dotenv": ">= 16.0.0", "dotenv-expand": ">= 10.0.0", "parse-json": ">= 5.2.0", "yaml": ">= 1.10.2"}, "peerDependencies": {"@nestjs/common": ">= 6.10.0 < 12", "reflect-metadata": ">= 0.1.12 < 0.3", "rxjs": ">= 6.0.0 < 8"}, "commitlint": {"extends": ["@latipun7/commitlintrc"]}, "release": {"extends": ["@latipun7/releaserc"]}, "lint-staged": {"**/*.ts": ["eslint --fix", "prettier --write"], "**/*": "prettier --write --ignore-unknown"}}